import { Entity, Vec3, Color, StandardMaterial, BoxGeometry, app, Mesh, MeshInstance, PlaneGeometry, BoundingBox, Material } from 'playcanvas';
import { Scene } from '../scene';
import { Splat } from '../splat';
import { Events } from '../events';
class CuttingBoxTool {
    activate: () => void;
    deactivate: () => void;

    boxEntity: Entity | null = null;
    faceEntities: Entity[] = [];
    currentDraggingFace: Entity | null = null;
    dragStartPoint = new Vec3();
    originalBoxSize = new Vec3();
    originalBoxCenter = new Vec3();
    scene: Scene;
    splat: Splat;
    events: Events;
    boundingBox: BoundingBox;
    nomalColor:Color;
    selectedColor:Color;
    constructor(events: Events,scene: Scene,parent: HTMLElement) {
        this.boxEntity = null;
        this.scene = scene;
        this.events = events;
        const start = { x: 0, y: 0 };
        const end = { x: 0, y: 0 };
        let dragId: number | undefined;
        let dragMoved = false;
        parent=this.scene.app.graphicsDevice.canvas;
        const pointerdown = (e: PointerEvent) => {
            if (e.altKey) {
                return;
            }
            if (dragId === undefined && (e.pointerType === 'mouse' ? e.button === 0 : e.isPrimary)) {
                e.preventDefault();
                e.stopPropagation();
                dragId = e.pointerId;
                dragMoved = false;
                parent.setPointerCapture(dragId);
                start.x = end.x = e.offsetX;
                start.y = end.y = e.offsetY;
                this.raycastFaces(e);
                UpdateSelectedPos();
            }
        };

        const pointermove = (e: PointerEvent) => {
            if (e.altKey) {
                return;
            }
            if ( e.pointerId === dragId) {
                e.preventDefault();
                e.stopPropagation();
                if (!this.currentDraggingFace) return;
                dragMoved = true;
                end.x = e.offsetX;
                end.y = e.offsetY;
                UpdateSelectedPos();
            }
        };
        const apply = (op: 'set' | 'add' | 'remove') => {
            const p = this.boxEntity.getPosition();
            const boxSize = this.boxEntity.getLocalScale(); // 获取包围盒的长宽高
            events.fire('select.byBox', op, [p.x, p.y, p.z, boxSize.x, boxSize.y, boxSize.z]);
            events.fire('select.invert');
        };
        const pointerup = (e: PointerEvent) => {
            if (e.altKey) {
                return;
            }
            if (e.pointerId === dragId) {
                e.preventDefault();
                e.stopPropagation();
                if (!this.currentDraggingFace) {
                    dragId = undefined;
                    return;
                }
                dragId = undefined;

                if (dragMoved) {
                    this.currentDraggingFace.render.meshInstances[0].material.opacity=0;
                    this.currentDraggingFace.render.meshInstances[0].material.update();
                    console.log("拖动完成，包围盒已更新");
                    // 更新splat的包围盒
                    if (this.boxEntity) {
                        apply('set');
                    }
                }
                // 重置当前拖动面
                this.currentDraggingFace = null;
                console.log("置空后：", this.currentDraggingFace);
            }
        };
        const UpdateSelectedPos = () => {
            if (!this.currentDraggingFace) return;
            const camera = this.scene.camera.entity.camera;
            const startVec3 = camera.screenToWorld(start.x, start.y, camera.farClip);
            const endVec3 = camera.screenToWorld(end.x, end.y, camera.farClip);
            const boxScale = this.boxEntity.getScale();
            const boxPosition = this.boxEntity.getPosition();
            // console.log('deltaX:',deltaX,'deltaY:',deltaY,"distance:",distance);
            start.x = end.x;
            start.y = end.y;
            // 获取面的名称来确定移动轴
            const faceName = this.currentDraggingFace.name;
            // let moveAxis = '';
            let newPosition = this.currentDraggingFace.getPosition();
            // console.log('pos:',newPosition);
            const distancex = endVec3.x - startVec3.x;
            const distancey = endVec3.y - startVec3.y;
            const distancez = endVec3.z - startVec3.z;
            // 根据面名称确定移动轴和方向
            if (faceName.includes('front')) {
                // moveAxis = 'z';
                newPosition = new Vec3(newPosition.x, newPosition.y, newPosition.z += distancez);
                boxScale.z += distancez;
                boxPosition.z += distancez / 2;
            } else if (faceName.includes('back')) {
                // moveAxis = 'z';
                newPosition = new Vec3(newPosition.x, newPosition.y, newPosition.z += distancez);
                boxScale.z -= distancez;
                boxPosition.z += distancez / 2;
            } else if (faceName.includes('left')) {
                // moveAxis = 'x';
                newPosition = new Vec3(newPosition.x += distancex, newPosition.y, newPosition.z);
                boxScale.x -= distancex;
                boxPosition.x += distancex / 2;
            } else if (faceName.includes('right')) {
                // moveAxis = 'x';
                newPosition = new Vec3(newPosition.x += distancex, newPosition.y, newPosition.z);
                boxScale.x += distancex;
                boxPosition.x += distancex / 2;
            } else if (faceName.includes('top')) {
                // moveAxis = 'y';
                newPosition = new Vec3(newPosition.x, newPosition.y += distancey, newPosition.z);
                boxScale.y += distancey;
                boxPosition.y += distancey / 2;
            } else if (faceName.includes('bottom')) {
                // moveAxis = 'y';
                newPosition = new Vec3(newPosition.x, newPosition.y += distancey, newPosition.z);
                boxScale.y -= distancey;
                boxPosition.y += distancey / 2;
            }
            this.boxEntity.setLocalScale(boxScale);
            this.boxEntity.setPosition(boxPosition);
            this.currentDraggingFace.setLocalPosition(newPosition);
            this.updateFacesPositions();
            this.scene.forceRender = true;
            //    console.log('After Pos:',newPosition,this.currentDraggingFace.name);
        };
     

        this.activate = () => {
            console.log("执行activate");
            // parent.style.display = 'block';
            parent.addEventListener('pointerdown', pointerdown);
            parent.addEventListener('pointermove', pointermove);
            parent.addEventListener('pointerup', pointerup);
            events.fire('cuttingBox-Activate');

        };

        this.deactivate = () => {
            console.log("执行deactivate");
            if (dragId !== undefined) {
                dragId = undefined;
            }
            events.fire('cuttingBox-Deactivate');
            parent.removeEventListener('pointerdown', pointerdown);
            parent.removeEventListener('pointermove', pointermove);
            parent.removeEventListener('pointerup', pointerup);
        };
        events.on('cuttingBox-Activate', async () => {
            await this.initialize();
            this.scene.forceRender = true;
        });
        events.on('cuttingBox-Deactivate', () => {
            this.clear();
            events.fire('select.none');
            this.scene.forceRender = true;
        });

    }
    // 监听测距模式切换事件


    async initialize() {
      this.nomalColor=Color.WHITE;
      this.selectedColor=Color.GREEN;
    
        // 创建包围盒实体
        await this.createBoundingBox();

        // 创建6个可拖动的面
        this.createDraggableFaces();

        // this.activate();
    }
    // 1.创建包围盒实体
    async createBoundingBox() {
        this.splat = this.getSelectedSplat();
        if (!this.splat) {
            console.log('请先选择一个模型');
            await this.events.invoke('showPopup', {
                type: 'error',
                header: '无法创建包围盒',
                message: `请先选择一个模型`
            });
            return;
        }
        console.log(this.splat.name);
        // 获取包围盒的最小值和最大值
        this.boundingBox = this.splat.worldBound;
        const geometry = new BoxGeometry();
        const mesh = Mesh.fromGeometry(app.graphicsDevice, geometry);
        const material = new StandardMaterial();
        material.emissive =Color.YELLOW;
        material.opacity = 0.3;
        material.blendType = 2;
        const meshinstance = new MeshInstance(mesh, material);
        this.boxEntity = new Entity('BoundingBox');
        this.boxEntity.addComponent('render', {
            meshInstances: [meshinstance],
        });
        this.boxEntity.setLocalPosition(this.boundingBox.center.x, this.boundingBox.center.y, this.boundingBox.center.z);
        // 设置包围盒的大小
        this.boxEntity.setLocalScale(this.boundingBox.halfExtents.x * 2, this.boundingBox.halfExtents.y * 2, this.boundingBox.halfExtents.z * 2);
        this.scene.app.root.addChild(this.boxEntity);

    }
    // 2.创建6个可拖动的面
    createDraggableFaces() {
        const faceNames = ['front', 'back', 'left', 'right', 'top', 'bottom'];
        const faceRotations = [
            new Vec3(90, 0, 0),    // front
            new Vec3(-90, 0, 0),  // back
            new Vec3(0, 0, 90),   // left
            new Vec3(0, 0, -90),  // right
            new Vec3(0, 0, 0),   // top
            new Vec3(180, 0, 0)   // bottom
        ];
        const min = this.boundingBox.getMin(); // 包围盒的最小值
        const max = this.boundingBox.getMax(); // 包围盒的最大值
        const facePositions = [
            new Vec3(this.boundingBox.center.x, this.boundingBox.center.y, this.boundingBox.center.z + this.boundingBox.halfExtents.z + 0.1),
            new Vec3(this.boundingBox.center.x, this.boundingBox.center.y, this.boundingBox.center.z - (max.z - min.z) / 2 - 0.1),
            new Vec3(this.boundingBox.center.x - (max.x - min.x) / 2 - 0.1, this.boundingBox.center.y, this.boundingBox.center.z), //left
            new Vec3(this.boundingBox.center.x + (max.x - min.x) / 2 + 0.1, this.boundingBox.center.y, this.boundingBox.center.z),//right
            new Vec3(this.boundingBox.center.x, this.boundingBox.center.y + (max.y - min.y) / 2 + 0.1, this.boundingBox.center.z),
            new Vec3(this.boundingBox.center.x, this.boundingBox.center.y - (max.y - min.y) / 2 - 0.1, this.boundingBox.center.z)
        ];

        const faceScales = [
            new Vec3(this.boundingBox.halfExtents.x * 2, 1, this.boundingBox.halfExtents.y * 2), // front  
            new Vec3(this.boundingBox.halfExtents.x * 2, 1, this.boundingBox.halfExtents.y * 2),
            new Vec3(this.boundingBox.halfExtents.y * 2, 1, this.boundingBox.halfExtents.z * 2),
            new Vec3(this.boundingBox.halfExtents.y * 2, 1, this.boundingBox.halfExtents.z * 2),
            new Vec3(this.boundingBox.halfExtents.x * 2, 1, this.boundingBox.halfExtents.z * 2),
            new Vec3(this.boundingBox.halfExtents.x * 2, 1, this.boundingBox.halfExtents.z * 2),
        ]
        for (let i = 0; i < faceNames.length; i++) {
            const geometry = new PlaneGeometry();
            const mesh = Mesh.fromGeometry(app.graphicsDevice, geometry);
            const material = new StandardMaterial();
            material.emissive = this.selectedColor;
            material.opacity = 0;
            material.blendType = 2;
            const meshinstance = new MeshInstance(mesh, material);
            const planeEntity = new Entity('plane_' + faceNames[i]);
            planeEntity.setEulerAngles(faceRotations[i]);
            planeEntity.setLocalPosition(facePositions[i]); // 设置位置为中心
            planeEntity.setLocalScale(new Vec3(faceScales[i].x * 0.9, faceScales[i].y, faceScales[i].z * 0.9)); // 设置缩放为包围盒的大小
            // 设置面的缩放
            planeEntity.addComponent('render', {
                meshInstances: [meshinstance],
            });
            planeEntity.addComponent('rigidbody', {
                type: 'static',  // 或 'dynamic'，取决于您的需求
                mass: 0
            });

            planeEntity.addComponent('collision', {
                type: 'box',// 或其他适合的形状
                halfExtents: new Vec3(faceScales[i].x / 2, 0.1, faceScales[i].z / 2)  // 确保碰撞形状与几何形状匹配
            });
            this.faceEntities.push(planeEntity);
            planeEntity.tags.add('draggableFace');
            this.scene.app.root.addChild(planeEntity);
        }
    }
    // 更新所有面的位置和大小
    updateFacesPositions() {
        if (!this.boxEntity) return;
        const boxPosition = this.boxEntity.getLocalPosition();
        const boxScale = this.boxEntity.getLocalScale();
        const halfExtents = new Vec3(boxScale.x / 2, boxScale.y / 2, boxScale.z / 2);

        const facePositions = [
            new Vec3(boxPosition.x, boxPosition.y, boxPosition.z + halfExtents.z + 0.1),  // front
            new Vec3(boxPosition.x, boxPosition.y, boxPosition.z - halfExtents.z - 0.1),  // back
            new Vec3(boxPosition.x - halfExtents.x - 0.1, boxPosition.y, boxPosition.z),  // left
            new Vec3(boxPosition.x + halfExtents.x + 0.1, boxPosition.y, boxPosition.z),  // right
            new Vec3(boxPosition.x, boxPosition.y + halfExtents.y + 0.1, boxPosition.z),  // top
            new Vec3(boxPosition.x, boxPosition.y - halfExtents.y - 0.1, boxPosition.z)   // bottom
        ];

        const faceScales = [
            new Vec3(boxScale.x * 0.9, 1, boxScale.y * 0.9),  // front
            new Vec3(boxScale.x * 0.9, 1, boxScale.y * 0.9),  // back
            new Vec3(boxScale.y * 0.9, 1, boxScale.z * 0.9),  // left
            new Vec3(boxScale.y * 0.9, 1, boxScale.z * 0.9),  // right
            new Vec3(boxScale.x * 0.9, 1, boxScale.z * 0.9),  // top
            new Vec3(boxScale.x * 0.9, 1, boxScale.z * 0.9)   // bottom
        ];
        this.faceEntities.forEach((face, index) => {
            face.setLocalPosition(facePositions[index]);
            face.setLocalScale(faceScales[index]);
        });
    }
    raycastFaces(event: { x: number; y: number }) {
        const camera = this.scene.camera.entity.camera;
        if (!camera) return;
        const start = camera.entity.getPosition();
        const end = camera.screenToWorld(event.x, event.y, camera.farClip)
        const hit = this.scene.app.systems.rigidbody.raycastFirst(
            start,
            end,
        );
        if (hit) {
            this.currentDraggingFace = hit.entity;
            this.currentDraggingFace.render.meshInstances[0].material.opacity=0.5;
            this.currentDraggingFace.render.meshInstances[0].material.update();
            this.scene.forceRender=true;
            console.log('射线命中对象:', hit.entity.name);
        } else {
            console.log('未命中任何对象');
        }
    }

    // 获取当前选中的 splat 模型
    getSelectedSplat = (): Splat => {
        return this.events.invoke('selection');
    }
    clear() {
        this.boxEntity?.destroy();
        this.faceEntities.forEach(face => {
            face.destroy();
        });
        this.boxEntity = null;
        this.faceEntities = [];
    }
}

export { CuttingBoxTool };
