import { Color, path, PROJECTION_ORTHOGRAPHIC, Vec3 } from 'playcanvas';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON> } from './drop-handler';
import { ElementType } from './element';
import { Events } from './events';
import { Scene } from './scene';
import { Writer, DownloadWriter, FileStreamWriter, BufferWriter } from './serialize/writer';
import { Splat } from './splat';
import { serializePly, serializePlyCompressed, SerializeSettings, serializeSplat, serializeViewer, ViewerExportSettings } from './splat-serialize';
import { localize } from './ui/localization';
import { UnicityPublishSettings, getUnicityUser, uploadToUnicity, getProjectData, UploadType ,checkDraftData} from './unicity-service';
import { ExperienceSettings } from './splat-serialize';
import { setGlobalExperienceSettings,getProjectId } from './sceneGlobalSetting';
// ts compiler and vscode find this type, but eslint does not
type FilePickerAcceptType = unknown;
interface RemoteStorageDetails {
    method: string;
    url: string;
}

type ExportType = 'ply' | 'compressed-ply' | 'splat' | 'viewer';

interface SceneWriteOptions {
    type: ExportType;
    filename?: string;
    stream?: FileSystemWritableFileStream;
    viewerExportSettings?: ViewerExportSettings
}

const filePickerTypes: { [key: string]: FilePickerAcceptType } = {
    'ply': {
        description: 'Gaussian Splat PLY File',
        accept: {
            'application/ply': ['.ply']
        }
    },
    'compressed-ply': {
        description: 'Compressed Gaussian Splat PLY File',
        accept: {
            'application/ply': ['.ply']
        }
    },
    'splat': {
        description: 'Gaussian Splat File',
        accept: {
            'application/x-gaussian-splat': ['.splat']
        }
    },
    'htmlViewer': {
        description: 'Viewer HTML',
        accept: {
            'text/html': ['.html']
        }
    },
    'packageViewer': {
        description: 'Viewer ZIP',
        accept: {
            'application/zip': ['.zip']
        }
    }
};

let fileHandle: FileSystemFileHandle = null;

const vec = new Vec3();

// download the data to the given filename
const download = (filename: string, data: Uint8Array) => {
    const blob = new Blob([data], { type: 'octet/stream' });
    const url = window.URL.createObjectURL(blob);

    const lnk = document.createElement('a');
    lnk.download = filename;
    lnk.href = url;

    // create a "fake" click-event to trigger the download
    if (document.createEvent) {
        const e = document.createEvent('MouseEvents');
        e.initMouseEvent('click', true, true, window,
            0, 0, 0, 0, 0, false, false, false,
            false, 0, null);
        lnk.dispatchEvent(e);
    } else {
        // @ts-ignore
        lnk.fireEvent?.('onclick');
    }

    window.URL.revokeObjectURL(url);
};

const loadCameraPoses = async (url: string, filename: string, events: Events) => {
    const response = await fetch(url);
    const json = await response.json();
    if (json.length > 0) {
        // calculate the average position of the camera poses
        const ave = new Vec3(0, 0, 0);
        json.forEach((pose: any) => {
            vec.set(pose.position[0], pose.position[1], pose.position[2]);
            ave.add(vec);
        });
        ave.mulScalar(1 / json.length);

        // sort entries by trailing number if it exists
        const sorter = (a: any, b: any) => {
            const avalue = a.id ?? a.img_name?.match(/\d*$/)?.[0];
            const bvalue = b.id ?? b.img_name?.match(/\d*$/)?.[0];
            return (avalue && bvalue) ? parseInt(avalue, 10) - parseInt(bvalue, 10) : 0;
        };

        json.sort(sorter).forEach((pose: any, i: number) => {
            if (pose.hasOwnProperty('position') && pose.hasOwnProperty('rotation')) {
                const p = new Vec3(pose.position);
                const z = new Vec3(pose.rotation[0][2], pose.rotation[1][2], pose.rotation[2][2]);

                const dot = vec.sub2(ave, p).dot(z);
                vec.copy(z).mulScalar(dot).add(p);

                events.fire('camera.addPose', {
                    name: pose.img_name ?? `${filename}_${i}`,
                    frame: i,
                    position: new Vec3(-p.x, -p.y, p.z),
                    target: new Vec3(-vec.x, -vec.y, vec.z)
                });
            }
        });
    }
};

// initialize file handler events
const initFileHandler = (scene: Scene, events: Events, dropTarget: HTMLElement, remoteStorageDetails: RemoteStorageDetails) => {

    // returns a promise that resolves when the file is loaded
    const handleImport = async (url: string, filename?: string, animationFrame = false) => {
        try {
            if (!filename) {
                // extract filename from url if one isn't provided
                try {
                    filename = new URL(url, document.baseURI).pathname.split('/').pop();
                } catch (e) {
                    filename = url;
                }
            }

            const lowerFilename = (filename || url).toLowerCase();
            if (lowerFilename.endsWith('.ssproj')) {
                await events.invoke('doc.dropped', new File([await (await fetch(url)).blob()], filename));
            } else if (lowerFilename.endsWith('.ply') || lowerFilename.endsWith('.splat') || (lowerFilename === 'meta.json')) {
                const model = await scene.assetLoader.loadModel({ url, filename, animationFrame });
                scene.add(model);
                //   //TODO：lw-裁切盒初始化
                //   const boundingBoxEditor = new BoundingBoxEditor(events,scene,model);
                //   boundingBoxEditor.initialize();
                return model;
            } else if (lowerFilename.endsWith('.json')) {
                await loadCameraPoses(url, filename, events);
            } else {
                throw new Error('Unsupported file type');
            }
        } catch (error) {
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('popup.error-loading'),
                message: `${error.message ?? error} while loading '${filename}'`
            });
        }
    };

    events.function('import', (url: string, filename?: string, animationFrame = false) => {
        return handleImport(url, filename, animationFrame);
    });

    // create a file selector element as fallback when showOpenFilePicker isn't available
    let fileSelector: HTMLInputElement;
    if (!window.showOpenFilePicker) {
        fileSelector = document.createElement('input');
        fileSelector.setAttribute('id', 'file-selector');
        fileSelector.setAttribute('type', 'file');
        fileSelector.setAttribute('accept', '.ply,.splat');
        fileSelector.setAttribute('multiple', 'true');

        fileSelector.onchange = async () => {
            const files = fileSelector.files;
            for (let i = 0; i < files.length; i++) {
                const file = fileSelector.files[i];
                const url = URL.createObjectURL(file);
                await handleImport(url, file.name);
                URL.revokeObjectURL(url);
            }
        };
        document.body.append(fileSelector);
    }

    // create the file drag & drop handler
    CreateDropHandler(dropTarget, async (entries, shift) => {
        // document load, only support a single file drop
        if (entries.length === 1 && entries[0].file?.name?.toLowerCase().endsWith('.ssproj')) {
            await events.invoke('doc.dropped', entries[0].file);
            return;
        }

        // filter out non supported extensions
        entries = entries.filter((entry) => {
            const name = entry.file?.name;
            if (!name) return false;
            const lowerName = name.toLowerCase();
            return lowerName.endsWith('.ply') || lowerName.endsWith('.splat') || lowerName.endsWith('.json');
        });

        if (entries.length === 0) {
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('popup.error-loading'),
                message: localize('popup.drop-files')
            });
        } else {
            // determine if all files share a common filename prefix followed by
            // a frame number, e.g. "frame0001.ply", "frame0002.ply", etc.
            const isSequence = () => {
                // eslint-disable-next-line regexp/no-super-linear-backtracking
                const regex = /(.*?)(\d+)(?:\.compressed)?\.ply$/;
                const baseMatch = entries[0].file.name?.toLowerCase().match(regex);
                if (!baseMatch) {
                    return false;
                }

                for (let i = 1; i < entries.length; i++) {
                    const thisMatch = entries[i].file.name?.toLowerCase().match(regex);
                    if (!thisMatch || thisMatch[1] !== baseMatch[1]) {
                        return false;
                    }
                }

                return true;
            };

            if (entries.length > 1 && isSequence()) {
                events.fire('plysequence.setFrames', entries.map(e => e.file));
                events.fire('timeline.frame', 0);
            } else {
                for (let i = 0; i < entries.length; i++) {
                    const entry = entries[i];
                    const url = URL.createObjectURL(entry.file);
                    await handleImport(url, entry.filename);
                    URL.revokeObjectURL(url);
                }
            }
        }
    });

    // get the list of visible splats containing gaussians
    const getSplats = () => {
        return (scene.getElementsByType(ElementType.splat) as Splat[])
        .filter(splat => splat.visible)
        .filter(splat => splat.numSplats > 0);
    };

    events.function('scene.allSplats', () => {
        return (scene.getElementsByType(ElementType.splat) as Splat[]);
    });

    events.function('scene.splats', () => {
        return getSplats();
    });

    events.function('scene.empty', () => {
        return getSplats().length === 0;
    });

    events.function('scene.import', async () => {
        if (fileSelector) {
            fileSelector.click();
        } else {
            try {
                const handles = await window.showOpenFilePicker({
                    id: 'SuperSplatFileOpen',
                    multiple: true,
                    types: [filePickerTypes.ply, filePickerTypes.splat]
                });
                for (let i = 0; i < handles.length; i++) {
                    const handle = handles[i];
                    const file = await handle.getFile();
                    const url = URL.createObjectURL(file);
                    await handleImport(url, file.name);
                    URL.revokeObjectURL(url);

                    if (i === 0) {
                        fileHandle = handle;
                    }
                }
            } catch (error) {
                if (error.name !== 'AbortError') {
                    console.error(error);
                }
            }
        }
    });
   //TODO：lw-重置场景
    events.function('scene.ResetData',async()=>{
        try {
           const projectID=getProjectId();
            const result = await events.invoke('showPopup', {
                type: 'yesno',
                header: localize('doc.reset'),
                message: localize(events.invoke('scene.dirty') ? 'doc.unsaved-message' : 'doc.reset-message')
            });

            if (result.action !== 'yes') {
                console.log("取消场景重置");
                return;
            }
            ClearSceneData();
            // 1.调用 getProjectData 获取项目数据
            const { splatUrl, jsonUrl } = await getProjectData(projectID, true);

            // 2.检查返回的 URL 是否有效
            if (!splatUrl) {
                throw new Error('获取的项目数据无效,缺少splat数据');
            }
            console.log('加载项目数据:', { splatUrl, jsonUrl });
            // 3.加载 splat 文件
            await events.invoke('scene.importOnline', splatUrl);

            // 4.加载 json 文件
            await events.invoke('scene.LoadAndParseJson', jsonUrl);

            // 显示提示
            await events.invoke('showPopup', {
                type: 'info',
                header: localize('doc.reset'),
                message: `已完成场景的重置，请开始编辑吧`
            });
        } catch (error) {
            console.error('场景重置失败:', error);

            // 显示错误弹窗
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('doc.reset'),
                message: `${error.message ?? error}`
            });
        }
    });
    //TODO：lw-清除当前场景数据
    const ClearSceneData = async () => {
        let keysNumber: number[] = [];
        keysNumber = events.invoke('timeline.keys');
        console.log(keysNumber.length,keysNumber);
        if (keysNumber != null && keysNumber.length > 0) {
            for (let i = keysNumber.length - 1; i >= 0; i--) {
                events.fire('timeline.remove', i);
            }
        }
        events.fire('camera.reset');
        events.fire('scene.clear');
    };
    //TODO：lw-初始化splat及json数据
    events.function('scene.InitSplatAndJson', async (projectId: string) => {
        try {
            // 1.调用 getProjectData 获取项目数据
            const { splatUrl, jsonUrl } = await getProjectData(projectId);

            // 2.检查返回的 URL 是否有效
            if (!splatUrl) {
                throw new Error('获取的项目数据无效,缺少splat数据');
            }
            console.log('加载项目数据:', { splatUrl, jsonUrl });

            // 3.加载 splat 文件
            await events.invoke('scene.importOnline', splatUrl);

            // 4.加载 json 文件
            await events.invoke('scene.LoadAndParseJson', jsonUrl);
            // await events.invoke('scene.importOnline', 'https://unicity3dev-res.bdnrc.org.cn/3dgs-unicity/2025-05-26/scanFiles_406949770094239744/point_cloud.splat');
            // 显示提示
            await events.invoke('showPopup', {
                type: 'info',
                header: '初始化场景成功',
                message: `已完成场景的初始化，请开始编辑吧`
            });
        } catch (error) {
            console.error('初始化项目场景失败:', error);

            // 显示错误弹窗
            await events.invoke('showPopup', {
                type: 'error',
                header: '初始化场景失败',
                message: `${error.message ?? error}`
            });
        }
    });

    //TODO:lw-添加对在线文件的支持
    events.function('scene.importOnline', async (url?: string) => {
        try {
            if (!url) {
                console.warn('splat数据无效');
                return;
            }
            // 验证 URL 是否有效
            const lowerFilename = url.toLowerCase();
            if (!lowerFilename.endsWith('.ply') && !lowerFilename.endsWith('.splat')) {
                throw new Error('仅支持 .ply 和 .splat 文件');
            }
            // 加载文件并处理
            await handleImport(url, url.split('/').pop());
            console.log('文件加载成功:', url);
        } catch (error) {
            console.error('加载在线文件失败:', error);
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('popup.error-loading'),
                message: `${error.message ?? error}`
            });
        }

    });
    //TODO:lw-添加在线json的支持
    events.function('scene.LoadAndParseJson', async (url?: string) => {
        try {
            if (!url) {
                console.warn('json数据无效');
                return;
            }
            // 验证 URL 是否有效
            const lowerFilename = url.toLowerCase();
            if (!lowerFilename.endsWith('.json')) {
                throw new Error('仅支持 .json 文件');
            }
            const response = await fetch(url,{cache:'no-store'});
            if (!response.ok) {
                throw new Error(`Failed to fetch JSON: ${response.statusText}`);
            }
            const jsonData = await response.json();
            setGlobalExperienceSettings(jsonData);
            InitSettingJson(jsonData);
            console.log('相机位姿加载成功:', jsonData);
        } catch (error) {
            console.error('加载在线相机位姿文件失败:', error);
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('popup.error-loading'),
                message: `${error.message ?? error}`
            });
        }
    });
    //TODO:lw-解析Json
    const InitSettingJson = async (jsonData: ExperienceSettings) => {
        try {
            //TODO:lw-判断是否为草稿数据，如果不是则转换坐标系
            const projectID=getProjectId();
            const isDraftData=await checkDraftData(projectID);
            if(!isDraftData)
            {
                jsonData.camera.position[0] = -jsonData.camera.position[0];
                jsonData.camera.position[1] = -jsonData.camera.position[1];
                jsonData.camera.target[0] = -jsonData.camera.target[0];
                jsonData.camera.target[1] = -jsonData.camera.target[1];
                if (jsonData.animTracks != null && jsonData.animTracks.length > 0) {
                    if (jsonData.animTracks[0].keyframes != null && jsonData.animTracks[0].keyframes.length > 0) {
                        jsonData.animTracks[0].keyframes.forEach(keyframe => {
                            keyframe.position[0] = -keyframe.position[0];
                            keyframe.position[1] = -keyframe.position[1];
                            keyframe.target[0] = -keyframe.target[0];
                            keyframe.target[1] = -keyframe.target[1];
                        });
                    }
                }

            }
            // 解析并应用相机设置
            if (jsonData.camera) {
                const { fov, position, target, startAnim, animTrack } = jsonData.camera;
                // 设置相机视角
                if (fov) {
                  events.fire('camera.setFov',fov);
                }

                // 设置相机位置和目标
                if (position && target) {
                    const pose = { position: new Vec3(), target: new Vec3() };
                    pose.position.set(position[0], position[1], position[2]);
                    pose.target.set(target[0], target[1], target[2]);
                    events.fire('camera.setPose', pose);
                }

                // 设置相机动画
                if (startAnim && animTrack) {
                    // 解析并应用动画轨迹
                    if (jsonData.animTracks) {
                        if (jsonData.animTracks.length > 0) {
                            const track = jsonData.animTracks.find(track => track.name === animTrack);
                            console.log(track.name);
                            const { name, duration, frameRate, target, loopMode, interpolation, keyframes } = track;
                            if (track) {
                                for (let i = 0; i < keyframes.length; ++i) {
                                    console.log(i);
                                    const camPose = { position: new Vec3(), target: new Vec3() };
                                    camPose.position.set(keyframes[i].position[0], keyframes[i].position[1], keyframes[i].position[2]);
                                    camPose.target.set(keyframes[i].target[0], keyframes[i].target[1], keyframes[i].target[2]);
                                    console.log(`添加相机位姿 ${name} at frame ${keyframes[i].times}`, camPose);
                                    events.fire('camera.addPose', {
                                        name: i.toString(),
                                        frame: keyframes[i].times,
                                        position: camPose.position,
                                        target: camPose.target
                                    });
                                }

                            } else {
                                console.warn(`动画轨迹 "${animTrack}" 未找到`);
                            }
                        }
                    }
                }
            }

            // 解析并应用背景颜色
            if (jsonData.background?.color) {
                const [r, g, b] = jsonData.background.color;
                events.fire('bgClr', new Color(r, g, b,1));
            }

      console.log('场景数据解析并应用成功');
    } catch (error) {
        console.error('解析或应用场景数据失败:', error);
    }
    };
    // open a folder
    events.function('scene.openAnimation', async () => {
        try {
            const handle = await window.showDirectoryPicker({
                id: 'SuperSplatFileOpenAnimation',
                mode: 'readwrite'
            });

            if (handle) {
                const files = [];
                for await (const value of handle.values()) {
                    if (value.kind === 'file') {
                        const file = await value.getFile();
                        if (file.name.toLowerCase().endsWith('.ply')) {
                            files.push(file);
                        }
                    }
                }
                events.fire('plysequence.setFrames', files);
                events.fire('timeline.frame', 0);
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error(error);
            }
        }
    });

    events.function('scene.export', async (type: ExportType, outputFilename: string = null, exportType: 'export' | 'saveAs' = 'export') => {
        const extensions = {
            'ply': '.ply',
            'compressed-ply': '.compressed.ply',
            'splat': '.splat',
            'viewer': '-viewer.html'
        };

        const removeExtension = (filename: string) => {
            return filename.substring(0, filename.length - path.getExtension(filename).length);
        };

        const replaceExtension = (filename: string, extension: string) => {
            return `${removeExtension(filename)}${extension}`;
        };

        const splats = getSplats();
        const splat = splats[0];
        let filename = outputFilename ?? replaceExtension(splat.filename, extensions[type]);

        const hasFilePicker = window.showSaveFilePicker;

        let viewerExportSettings;
        if (type === 'viewer') {
            // show viewer export options
            viewerExportSettings = await events.invoke('show.viewerExportPopup', hasFilePicker ? null : filename);

            // return if user cancelled
            if (!viewerExportSettings) {
                return;
            }

            if (hasFilePicker) {
                filename = replaceExtension(filename, viewerExportSettings.type === 'html' ? '.html' : '.zip');
            } else {
                filename = viewerExportSettings.filename;
            }
        }

        if (hasFilePicker) {
            try {
                const filePickerType = type === 'viewer' ? (viewerExportSettings.type === 'html' ? filePickerTypes.htmlViewer : filePickerTypes.packageViewer) : filePickerTypes[type];

                const fileHandle = await window.showSaveFilePicker({
                    id: 'SuperSplatFileExport',
                    types: [filePickerType],
                    suggestedName: filename
                });
                await events.invoke('scene.write', {
                    type,
                    stream: await fileHandle.createWritable(),
                    viewerExportSettings
                });
            } catch (error) {
                if (error.name !== 'AbortError') {
                    console.error(error);
                }
            }
        } else {
            await events.invoke('scene.write', { type, filename, viewerExportSettings });
        }
    });

    const writeScene = async (type: ExportType, writer: Writer, viewerExportSettings?: ViewerExportSettings) => {
        const splats = getSplats();
        const events = splats[0].scene.events;

        const serializeSettings: SerializeSettings = {
            maxSHBands: events.invoke('view.bands')
        };

        switch (type) {
            case 'ply':
                await serializePly(splats, serializeSettings, writer);
                break;
            case 'compressed-ply':
                serializeSettings.minOpacity = 1 / 255;
                serializeSettings.removeInvalid = true;
                await serializePlyCompressed(splats, serializeSettings, writer);
                break;
            case 'splat':
                await serializeSplat(splats, serializeSettings, writer);
                break;
            case 'viewer':
                await serializeViewer(splats, viewerExportSettings, writer);
                break;
        }
    };

    events.function('scene.write', async (options: SceneWriteOptions) => {
        events.fire('startSpinner');

        try {
            // setTimeout so spinner has a chance to activate
            await new Promise<void>((resolve) => {
                setTimeout(resolve);
            });

            const { stream, filename, type, viewerExportSettings } = options;
            const writer = stream ? new FileStreamWriter(stream) : new DownloadWriter(filename);

            await writeScene(type, writer, viewerExportSettings);
            await writer.close();
        } catch (error) {
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('popup.error-loading'),
                message: `${error.message ?? error} while saving file`
            });
        } finally {
            events.fire('stopSpinner');
        }
    });

    events.function('scene.saveUnicity', async (publishSettings: UnicityPublishSettings) => {
        // 检查用户是否已登录
        const user = await getUnicityUser();
        if (!user) {
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('publish.failed'),
                message: localize('publish.please-log-in')
            });
            return false;
        }

        try {
            events.fire('startSpinner');

            // 延迟一下，让spinner有机会显示
            await new Promise<void>((resolve) => {
                setTimeout(resolve, 10);
            });

            const splats = getSplats();
            if (splats.length === 0) {
                throw new Error(localize('scene.empty'));
            }

            // 1. 序列化.splat文件
            const splatWriter = new BufferWriter();
            await serializeSplat(splats, publishSettings.serializeSettings, splatWriter);
            const splatBuffer = splatWriter.close();

            // 2. 创建settings.json内容
            const settingsJson = JSON.stringify(publishSettings.experienceSettings, null, 4);

            // 3. 上传到Unicity云服务
            const response = await uploadToUnicity(
                splatBuffer,
                settingsJson,
                publishSettings,
                user,
                UploadType.SAVE_DRAFT
            );

            console.log('上传到Unicity云服务成功:', response);

            return response;
        } catch (error) {
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('publish.failed'),
                message: `${error.message ?? error}`
            });
            return null;
        } finally {
            events.fire('stopSpinner');
        }
    });

    /**
     * 发布场景到Unicity云服务
     * 导出.splat文件和settings.json文件，并上传到Unicity云服务
     */
    events.function('scene.publishUnicity', async (publishSettings: UnicityPublishSettings) => {
        // 检查用户是否已登录
        const user = await getUnicityUser();
        if (!user) {
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('publish.failed'),
                message: localize('publish.please-log-in')
            });
            return false;
        }

        try {
            events.fire('startSpinner');

            // 延迟一下，让spinner有机会显示
            await new Promise<void>((resolve) => {
                setTimeout(resolve, 10);
            });

            const splats = getSplats();
            if (splats.length === 0) {
                throw new Error(localize('scene.empty'));
            }
            // 1. 序列化.splat文件
            const splatWriter = new BufferWriter();
            await serializeSplat(splats, publishSettings.serializeSettings, splatWriter);
            const splatBuffer = splatWriter.close();
           //TODO:lw-发布到UniCity时转换坐标
            publishSettings.experienceSettings.camera.position[0] = -publishSettings.experienceSettings.camera.position[0];
            publishSettings.experienceSettings.camera.position[1] = -publishSettings.experienceSettings.camera.position[1];
            publishSettings.experienceSettings.camera.target[0] = -publishSettings.experienceSettings.camera.target[0];
            publishSettings.experienceSettings.camera.target[1] = -publishSettings.experienceSettings.camera.target[1];
            if (publishSettings.experienceSettings.animTracks != null && publishSettings.experienceSettings.animTracks.length > 0) {
                if (publishSettings.experienceSettings.animTracks[0].keyframes != null && publishSettings.experienceSettings.animTracks[0].keyframes.length > 0) {
                    publishSettings.experienceSettings.animTracks[0].keyframes.forEach(keyframe => {
                        keyframe.position[0] = -keyframe.position[0];
                        keyframe.position[1] = -keyframe.position[1];
                        keyframe.target[0] = -keyframe.target[0];
                        keyframe.target[1] = -keyframe.target[1];
                    });
                }
            }

            // 2. 创建settings.json内容
            const settingsJson = JSON.stringify(publishSettings.experienceSettings, null, 4);

            // 3. 上传到Unicity云服务
            const response = await uploadToUnicity(
                splatBuffer,
                settingsJson,
                publishSettings,
                user,
                UploadType.PUBLISH
            );

            console.log('上传到Unicity云服务成功:', response);

            return response;
        } catch (error) {
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('publish.failed'),
                message: `${error.message ?? error}`
            });
            return null;
        } finally {
            events.fire('stopSpinner');
        }
    });

    // 生成增强的Unicity预览，包含viewer功能
    events.function('scene.generateUnicityPreviewWithViewer', async (publishSettings: UnicityPublishSettings) => {
        events.fire('startSpinner');

        try {
            const splats = getSplats();
            if (splats.length === 0) {
                throw new Error(localize('scene.empty'));
            }

            // 序列化为PLY格式用于预览
            const plyWriter = new BufferWriter();
            await serializePly(splats, publishSettings.serializeSettings, plyWriter);
            const plyBuffer = plyWriter.close();

            // 创建临时的PLY文件Blob URL
            const plyBlob = new Blob([plyBuffer], { type: 'application/octet-stream' });
            const plyUrl = URL.createObjectURL(plyBlob);


            // 创建settings.json内容
            const settingsJson = JSON.stringify(publishSettings.experienceSettings, null, 2);

            // 创建增强的viewer HTML，包含完整的相机控制和动画功能
            const viewerHtml = `
<!DOCTYPE html>
<html lang="en">
    <head>
        <title>Unicity Preview</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
        <style>
            body, html {
                margin: 0;
                padding: 0;
                width: 100%;
                height: 100%;
                overflow: hidden;
                background-color: #000;
                font-family: Arial, sans-serif;
            }
            pc-app {
                width: 100%;
                height: 100%;
                display: block;
            }
            #loading {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: #000;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }
            #loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #333;
                border-top: 4px solid #ff6600;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-bottom: 20px;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            #loading-text {
                color: #fff;
                font-size: 16px;
            }
            .preview-notice {
                position: absolute;
                top: 10px;
                left: 10px;
                background-color: rgba(255, 102, 0, 0.9);
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }
        </style>
        <script type="importmap">
        {
            "imports": {
                "playcanvas": "https://cdn.jsdelivr.net/npm/playcanvas@2.7.6/build/playcanvas.mjs"
            }
        }
        </script>
        <script type="module">
            import * as pc from 'playcanvas';

            // 设置数据
            const settings = ${settingsJson};
            const plyUrl = '${plyUrl}';

            // 创建应用
            const canvas = document.createElement('canvas');
            document.body.appendChild(canvas);

            const app = new pc.Application(canvas, {
                mouse: new pc.Mouse(canvas),
                touch: new pc.TouchDevice(canvas),
                keyboard: new pc.Keyboard(window),
                graphicsDeviceOptions: {
                    antialias: true,
                    alpha: false
                }
            });

            // disable auto render, we'll render only when camera changes (与supersplat-viewer一致)
            app.autoRender = true;

            // 设置画布大小
            app.setCanvasFillMode(pc.FILLMODE_FILL_WINDOW);
            app.setCanvasResolution(pc.RESOLUTION_AUTO);

            // 创建相机
            const camera = new pc.Entity('camera');
            camera.addComponent('camera', {
                clearColor: new pc.Color(settings.background?.color?.[0] || 0,
                                       settings.background?.color?.[1] || 0,
                                       settings.background?.color?.[2] || 0),
                fov: settings.camera?.fov || 50
            });
            app.root.addChild(camera);

            // // handle horizontal fov on canvas resize（与supersplat-viewer和编辑模式一致）
            // const updateHorizontalFov = () => {
            //     camera.camera.horizontalFov = app.graphicsDevice.width > app.graphicsDevice.height;
            // };
            // app.graphicsDevice.on('resizecanvas', () => {
            //     updateHorizontalFov();
            //     app.renderNextFrame = true;
            // });
            // // 立即设置 horizontalFov，确保后续计算正确
            // updateHorizontalFov();

            // const prevProj = new pc.Mat4();
            // const prevWorld = new pc.Mat4();

            // app.on('framerender', () => {
            //     const world = camera.getWorldTransform();
            //     const proj = camera.camera.projectionMatrix;
            //     const nearlyEquals = (a, b, epsilon = 1e-4) => {
            //         return !a.some((v, i) => Math.abs(v - b[i]) >= epsilon);
            //     };

            //     // 与supersplat-viewer一致的渲染控制逻辑
            //     if (!app.autoRender && !app.renderNextFrame) {
            //         if (!nearlyEquals(world.data, prevWorld.data) ||
            //             !nearlyEquals(proj.data, prevProj.data)) {
            //             app.renderNextFrame = true;
            //         }
            //     }

            //     if (app.renderNextFrame) {
            //         prevWorld.copy(world);
            //         prevProj.copy(proj);
            //     }

            //     // suppress rendering till we're ready
            //     if (!state.readyToRender) {
            //         app.renderNextFrame = false;
            //     }
            // });

            // 创建光源
            const light = new pc.Entity('light');
            light.addComponent('light', {
                type: pc.LIGHTTYPE_DIRECTIONAL,
                color: new pc.Color(1, 1, 1),
                intensity: 1
            });
            light.setEulerAngles(45, 30, 0);
            app.root.addChild(light);

            // 数学工具函数
            const mod = (a, b) => ((a % b) + b) % b;

            // 四元数工具类
            class MyQuat extends pc.Quat {
                setFromAxisAngle(axis, angle) {
                    const halfAngle = angle * 0.5;
                    const s = Math.sin(halfAngle);
                    this.x = axis.x * s;
                    this.y = axis.y * s;
                    this.z = axis.z * s;
                    this.w = Math.cos(halfAngle);
                    return this;
                }
            }

            // 三次样条插值类
            class CubicSpline {
                constructor(times, points, stride) {
                    this.times = times;
                    this.points = points;
                    this.stride = stride;
                    this.segments = [];
                    this.buildSegments();
                }

                buildSegments() {
                    const n = this.times.length;
                    if (n < 2) return;

                    for (let i = 0; i < n - 1; i++) {
                        const t0 = this.times[i];
                        const t1 = this.times[i + 1];
                        const dt = t1 - t0;

                        const segment = {
                            t0: t0,
                            t1: t1,
                            dt: dt,
                            coeffs: []
                        };

                        for (let j = 0; j < this.stride; j++) {
                            const p0 = this.points[i * this.stride + j];
                            const p1 = this.points[(i + 1) * this.stride + j];

                            // 计算切线（使用相邻点的斜率）
                            let m0, m1;

                            if (i === 0) {
                                // 第一个点：使用前向差分
                                m0 = (p1 - p0) / dt;
                            } else {
                                // 使用中心差分
                                const p_prev = this.points[(i - 1) * this.stride + j];
                                const dt_prev = this.times[i] - this.times[i - 1];
                                m0 = ((p1 - p0) / dt + (p0 - p_prev) / dt_prev) * 0.5;
                            }

                            if (i === n - 2) {
                                // 最后一个点：使用后向差分
                                m1 = (p1 - p0) / dt;
                            } else {
                                // 使用中心差分
                                const p_next = this.points[(i + 2) * this.stride + j];
                                const dt_next = this.times[i + 2] - this.times[i + 1];
                                m1 = ((p_next - p1) / dt_next + (p1 - p0) / dt) * 0.5;
                            }

                            // Hermite插值系数
                            segment.coeffs[j] = {
                                a: p0,
                                b: m0 * dt,
                                c: 3 * (p1 - p0) - 2 * m0 * dt - m1 * dt,
                                d: 2 * (p0 - p1) + m0 * dt + m1 * dt
                            };
                        }

                        this.segments.push(segment);
                    }
                }

                evaluate(time, result) {
                    if (this.segments.length === 0) {
                        // 如果没有段，返回默认值
                        for (let i = 0; i < this.stride; i++) {
                            result[i] = 0;
                        }
                        return;
                    }

                    // 确保result数组有足够的长度
                    while (result.length < this.stride) {
                        result.push(0);
                    }

                    // 找到对应的时间段
                    let segment = this.segments[0];
                    for (let i = 0; i < this.segments.length; i++) {
                        if (time >= this.segments[i].t0 && time <= this.segments[i].t1) {
                            segment = this.segments[i];
                            break;
                        }
                    }

                    // 如果时间超出范围，使用最后一个段
                    if (time > this.segments[this.segments.length - 1].t1) {
                        segment = this.segments[this.segments.length - 1];
                    }

                    const t = segment.dt > 0 ? (time - segment.t0) / segment.dt : 0;

                    for (let i = 0; i < this.stride; i++) {
                        const coeff = segment.coeffs[i];
                        if (coeff) {
                            result[i] = coeff.a + coeff.b * t + coeff.c * t * t + coeff.d * t * t * t;
                        } else {
                            result[i] = 0;
                        }
                    }
                }

                static fromPointsLooping(duration, times, points, stride) {
                    return new CubicSpline(times, points, stride);
                }
            }

            // 动画游标类
            class AnimCursor {
                constructor(duration = 0, loopMode = 'none') {
                    this.duration = duration;
                    this.loopMode = loopMode;
                    this.timer = 0;
                    this.cursor = 0;
                }

                update(deltaTime) {
                    this.timer += deltaTime;
                    this.cursor += deltaTime;

                    if (this.cursor >= this.duration) {
                        switch (this.loopMode) {
                            case 'none':
                                this.cursor = this.duration;
                                break;
                            case 'repeat':
                                this.cursor %= this.duration;
                                break;
                            case 'pingpong':
                                this.cursor %= (this.duration * 2);
                                break;
                        }
                    }
                }

                reset(duration, loopMode) {
                    this.duration = duration;
                    this.loopMode = loopMode;
                    this.timer = 0;
                    this.cursor = 0;
                }

                set value(value) {
                    this.cursor = mod(value, this.duration);
                }

                get value() {
                    return this.cursor > this.duration ? this.duration - this.cursor : this.cursor;
                }
            }

            // 动画相机类
            class AnimCamera {
                constructor(spline, duration, loopMode, frameRate) {
                    this.spline = spline;
                    this.cursor = new AnimCursor(duration, loopMode);
                    this.frameRate = frameRate;
                    this.result = [];
                    this.position = new pc.Vec3();
                    this.target = new pc.Vec3();
                    this.rotateSpeed = 0.2;
                    this.rotation = new pc.Vec3();

                    // 初始化到起始帧
                    this.update(0, null);
                }

                update(deltaTime, input) {
                    // 更新动画游标
                    this.cursor.update(deltaTime);

                    // 计算样条插值
                    if (this.spline) {
                        this.spline.evaluate(this.cursor.value * this.frameRate, this.result);

                        if (this.result.every(isFinite)) {
                            this.position.set(this.result[0], this.result[1], this.result[2]);
                            this.target.set(this.result[3], this.result[4], this.result[5]);
                        }
                    }

                    // 处理旋转输入
                    if (input?.rotate) {
                        if (input.rotate.events && input.rotate.events.indexOf('up') !== -1) {
                            this.rotation.set(0, 0, 0);
                        } else if (input.rotate.value) {
                            this.rotation.x = Math.max(-90, Math.min(90, this.rotation.x - input.rotate.value[1] * this.rotateSpeed));
                            this.rotation.y = Math.max(-180, Math.min(180, this.rotation.y - input.rotate.value[0] * this.rotateSpeed));
                        }
                    }
                }

                getPose(pose) {
                    // 使用Pose.fromLookAt方法，与supersplat-viewer保持一致
                    pose.fromLookAt(this.position, this.target);

                    // 应用额外的旋转
                    if (this.rotation.x !== 0 || this.rotation.y !== 0) {
                        const q = new MyQuat();
                        const qx = new MyQuat();
                        const qy = new MyQuat();

                        qx.setFromAxisAngle(pc.Vec3.RIGHT, this.rotation.x * Math.PI / 180);
                        qy.setFromAxisAngle(pc.Vec3.UP, this.rotation.y * Math.PI / 180);

                        q.mul2(pose.rotation, qx);
                        pose.rotation.mul2(qy, q);
                    }
                }

                reset() {
                    this.cursor.reset(this.cursor.duration, this.cursor.loopMode);
                    this.rotation.set(0, 0, 0);
                }

                static fromTrack(track) {
                    const { keyframes, duration, frameRate, loopMode } = track;

                    // 构建包含位置和目标的点数组
                    const points = [];
                    for (let i = 0; i < keyframes.length; i++) {
                        points.push(keyframes[i].position[0], keyframes[i].position[1],keyframes[i].position[2]);
                        points.push(keyframes[i].target[0], keyframes[i].target[1],keyframes[i].target[2]);
                    }

                    const times = keyframes.map(k => k.times);
                    const extra = (duration === times[times.length - 1] / frameRate) ? 1 : 0;
                    const spline = CubicSpline.fromPointsLooping((duration + extra) * frameRate, times, points, 6);

                    return new AnimCamera(spline, duration, loopMode, frameRate);
                }
            }

            // 状态管理（与supersplat-viewer一致）
            const state = {
                readyToRender: false,       // don't render till this is set
                cameraMode: 'orbit',        // 当前相机控制模式：orbit, fly
                animationTime: 0,
                animationDuration: 0,
                animationPaused: true,      // 动画播放状态
                animationPlaying: false,    // 是否正在播放动画
                hasAnimation: false
            };

            // 相机控制器
            let orbitCamera, flyCamera, animCamera;
            let currentController;

            // 初始化相机控制器
            const initCameraControllers = () => {
                // 轨道相机控制器
                orbitCamera = {
                    focus: new pc.Vec3(0, 0, 0),
                    distance: 0,
                    pitch: 0,
                    yaw: 0,
                    // 保存初始状态
                    initialFocus: new pc.Vec3(0, 0, 0),
                    initialDistance: 0,
                    initialPitch: 0,
                    initialYaw: 0,
                    update: function(dt) {
                        const position = new pc.Vec3();
                        position.x = this.focus.x + this.distance * Math.sin(this.yaw) * Math.cos(this.pitch);
                        position.y = this.focus.y + this.distance * Math.sin(this.pitch);
                        position.z = this.focus.z + this.distance * Math.cos(this.yaw) * Math.cos(this.pitch);

                        camera.setPosition(position);
                        camera.lookAt(this.focus);
                    },
                    reset: function(pose) {
                        if (pose) {
                            // 如果提供了pose，从pose设置相机参数
                            const position = pose.position;
                            const rotation = pose.rotation;

                            // 计算相机朝向
                            const forward = new pc.Vec3(0, 0, -1);
                            const euler = new pc.Vec3();
                            rotation.getEulerAngles(euler);
                            const rotMatrix = new pc.Mat4().setFromEulerAngles(euler.x, euler.y, euler.z);
                            rotMatrix.transformVector(forward, forward);

                            // 使用当前距离投射出焦点位置，如果没有当前距离则使用默认值
                            const currentDistance = this.distance || 5.0;
                            this.focus.copy(position).add(forward.scale(currentDistance));

                            // 计算从焦点到相机的向量
                            const focusToCamera = new pc.Vec3();
                            focusToCamera.sub2(position, this.focus);

                            // 更新距离
                            this.distance = focusToCamera.length();

                            // 计算pitch和yaw
                            const dir = focusToCamera.clone().normalize();
                            this.pitch = Math.asin(Math.max(-1, Math.min(1, dir.y)));
                            this.yaw = Math.atan2(dir.x, dir.z);
                        } else {
                            // 如果没有提供pose，重置到初始状态
                            this.focus.copy(this.initialFocus);
                            this.distance = this.initialDistance;
                            this.pitch = this.initialPitch;
                            this.yaw = this.initialYaw;
                        }
                    },
                    saveInitialState: function() {
                        this.initialFocus.copy(this.focus);
                        this.initialDistance = this.distance;
                        this.initialPitch = this.pitch;
                        this.initialYaw = this.yaw;
                    },
                    getPose: function(pose) {
                        const position = new pc.Vec3();
                        position.x = this.focus.x + this.distance * Math.sin(this.yaw) * Math.cos(this.pitch);
                        position.y = this.focus.y + this.distance * Math.sin(this.pitch);
                        position.z = this.focus.z + this.distance * Math.cos(this.yaw) * Math.cos(this.pitch);

                        pose.position.copy(position);

                        // 计算旋转
                        const direction = new pc.Vec3();
                        direction.sub2(this.focus, position).normalize();
                        const up = new pc.Vec3(0, 1, 0);
                        const right = new pc.Vec3();
                        right.cross(direction, up).normalize();
                        up.cross(right, direction).normalize();

                        const rotMatrix = new pc.Mat4();
                        rotMatrix.data[0] = right.x; rotMatrix.data[1] = right.y; rotMatrix.data[2] = right.z;
                        rotMatrix.data[4] = up.x; rotMatrix.data[5] = up.y; rotMatrix.data[6] = up.z;
                        rotMatrix.data[8] = -direction.x; rotMatrix.data[9] = -direction.y; rotMatrix.data[10] = -direction.z;

                        pose.rotation.setFromMat4(rotMatrix);
                    }
                };

                // 飞行相机控制器
                flyCamera = {
                    position: new pc.Vec3(0, 0, 0),
                    rotation: new pc.Vec3(0, 0, 0),
                    speed: 2,
                    // 保存初始状态
                    initialPosition: new pc.Vec3(0, 0, 0),
                    initialRotation: new pc.Vec3(0, 0, 0),
                    update: function(dt) {
                        camera.setPosition(this.position);
                        camera.setEulerAngles(this.rotation);
                    },
                    reset: function(pose) {
                        if (pose) {
                            // 如果提供了pose，从pose设置相机参数
                            this.position.copy(pose.position);

                            // 从四元数计算前向向量，然后转换为欧拉角
                            const forward = new pc.Vec3(0, 0, -1);
                            pose.rotation.transformVector(forward, forward);
                            forward.normalize();

                            // 计算yaw (水平旋转)
                            const yaw = -Math.atan2(forward.x, -forward.z) * 180 / Math.PI;

                            // 计算pitch (垂直旋转)
                            const pitch = Math.asin(Math.max(-1, Math.min(1, forward.y))) * 180 / Math.PI;

                            // 设置fly相机的旋转，确保roll为0
                            this.rotation.set(pitch, yaw, 0);
                        } else {
                            // 如果没有提供pose，重置到初始状态
                            this.position.copy(this.initialPosition);
                            this.rotation.copy(this.initialRotation);
                        }
                    },
                    saveInitialState: function() {
                        this.initialPosition.copy(this.position);
                        this.initialRotation.copy(this.rotation);
                    },
                    getPose: function(pose) {
                        pose.position.copy(this.position);
                        pose.rotation.setFromEulerAngles(this.rotation.x, this.rotation.y, this.rotation.z);
                    }
                };

                // 动画相机将在splatEntity创建后单独初始化
                animCamera = null;

                // 设置默认控制器
                currentController = orbitCamera;
            };

            // 切换相机模式
            const setCameraMode = (mode) => {
                // 保存当前相机的pose
                const currentPose = new Pose();

                // 获取当前相机的pose
                if (state.cameraMode === 'anim' && animCamera) {
                    // 如果动画正在播放，从动画相机获取当前pose
                    animCamera.getPose(currentPose);
                } else {
                    // 否则从当前控制器获取pose
                    if (currentController && currentController.getPose) {
                        currentController.getPose(currentPose);
                    } else {
                        // 如果没有控制器，从相机实体获取当前位置
                        currentPose.position.copy(camera.getPosition());
                        currentPose.rotation.copy(camera.getRotation());
                    }
                }

                // 只有orbit和fly是真正的相机控制模式
                if (mode === 'orbit' || mode === 'fly') {
                    const prevMode = state.cameraMode;
                    state.cameraMode = mode;

                    // 更新控制器
                    const newController = mode === 'orbit' ? orbitCamera : flyCamera;

                    // 如果切换到不同的相机模式，用当前pose重置新相机
                    if (prevMode !== mode && newController && newController.reset) {
                        newController.reset(currentPose);
                    }

                    currentController = newController;
                }
                // anim模式：切换到动画相机模式
                else if (mode === 'anim') {
                    state.cameraMode = 'anim';
                    state.animationPlaying = true;
                    state.animationPaused = false;
                }
            };

            // 播放动画
            const playAnimation = () => {
                state.animationPlaying = true;
                state.animationPaused = false;
            };

            // 暂停动画
            const pauseAnimation = () => {
                //console.log('暂停动画，同步相机位置');
                state.animationPaused = true;
            };

            // 停止动画
            const stopAnimation = () => {
                state.animationPlaying = false;
                state.animationPaused = true;
            };

            // 创建Pose类用于相机姿态
            class Pose {
                constructor() {
                    this.position = new pc.Vec3();
                    this.rotation = new pc.Quat();
                }

                copy(other) {
                    this.position.copy(other.position);
                    this.rotation.copy(other.rotation);
                }

                lerp(from, to, t) {
                    this.position.lerp(from.position, to.position, t);
                    this.rotation.slerp(from.rotation, to.rotation, t);
                }

                // 实现fromLookAt方法，参考supersplat-viewer
                fromLookAt(position, target) {
                    this.position.copy(position);

                    // 计算朝向
                    const direction = new pc.Vec3();
                    direction.sub2(target, position).normalize();

                    // 计算up向量
                    const up = new pc.Vec3(0, 1, 0);
                    const right = new pc.Vec3();
                    right.cross(direction, up).normalize();
                    up.cross(right, direction).normalize();

                    // 创建旋转矩阵并转换为四元数
                    const rotMatrix = new pc.Mat4();
                    rotMatrix.data[0] = right.x; rotMatrix.data[1] = right.y; rotMatrix.data[2] = right.z;
                    rotMatrix.data[4] = up.x; rotMatrix.data[5] = up.y; rotMatrix.data[6] = up.z;
                    rotMatrix.data[8] = -direction.x; rotMatrix.data[9] = -direction.y; rotMatrix.data[10] = -direction.z;

                    this.rotation.setFromMat4(rotMatrix);

                    return this;
                }
            }

            // 鼠标控制
            let mouseButtons = {
                left: false,
                right: false
            };
            let lastMouseX = 0;
            let lastMouseY = 0;

            // 双击检测
            let lastClickTime = 0;
            let lastClickX = 0;
            let lastClickY = 0;
            const DOUBLE_CLICK_TIME = 300; // 毫秒
            const DOUBLE_CLICK_DISTANCE = 5; // 像素

            // 双击处理函数 - 选择焦点
            const handleDoubleClick = async (screenX, screenY) => {
                try {
                    // 将屏幕坐标转换为canvas坐标
                    const rect = canvas.getBoundingClientRect();
                    const canvasX = screenX - rect.left;
                    const canvasY = screenY - rect.top;

                    // 创建射线进行拾取
                    const cameraPos = camera.getPosition();
                    const ray = new pc.Ray();

                    // 将屏幕坐标转换为世界坐标射线
                    camera.camera.screenToWorld(canvasX, canvasY, 1.0, ray.direction);
                    ray.direction.sub(cameraPos).normalize();
                    ray.origin.copy(cameraPos);

                    // 查找最近的splat点
                    let closestDistance = Infinity;
                    let closestPoint = null;

                    // 遍历场景中的所有子节点，寻找GSplat实体
                    const findSplatEntities = (node) => {
                        const entities = [];
                        if (node.gsplat) {
                            entities.push(node);
                        }
                        for (let i = 0; i < node.children.length; i++) {
                            entities.push(...findSplatEntities(node.children[i]));
                        }
                        return entities;
                    };

                    const splatEntities = findSplatEntities(app.root);

                    for (const splatEntity of splatEntities) {
                        if (!splatEntity.gsplat || !splatEntity.gsplat.instance) continue;

                        const splat = splatEntity.gsplat.instance.splat;
                        if (!splat || !splat.aabb) continue;

                        // 简单的包围盒相交测试
                        const aabb = splat.aabb;
                        const intersectionPoint = new pc.Vec3();

                        // 检查射线与包围盒的相交
                        if (aabb.intersectsRay(ray, intersectionPoint)) {
                            const distance = intersectionPoint.distance(cameraPos);
                            if (distance < closestDistance) {
                                closestDistance = distance;
                                closestPoint = intersectionPoint.clone();
                            }
                        }
                    }

                    // 如果找到了交点，更新orbit相机的焦点
                    if (closestPoint) {
                        orbitCamera.focus.copy(closestPoint);
                        //console.log('双击选择新焦点:', closestPoint);
                    } else {
                        // 如果没有找到splat，使用一个默认的深度投射点
                        const defaultDepth = orbitCamera.distance;
                        const worldPoint = new pc.Vec3();
                        camera.camera.screenToWorld(canvasX, canvasY, defaultDepth, worldPoint);
                        orbitCamera.focus.copy(worldPoint);
                        //console.log('双击选择默认焦点:', worldPoint);
                    }
                } catch (error) {
                    console.warn('双击选择焦点失败:', error);
                }
            };

            canvas.addEventListener('mousedown', (e) => {
                if (e.button === 0) { // 左键
                    mouseButtons.left = true;

                    // 双击检测（仅在orbit模式下）
                    if (state.cameraMode === 'orbit') {
                        const currentTime = Date.now();
                        const deltaTime = currentTime - lastClickTime;
                        const deltaX = Math.abs(e.clientX - lastClickX);
                        const deltaY = Math.abs(e.clientY - lastClickY);

                        if (deltaTime < DOUBLE_CLICK_TIME && deltaX < DOUBLE_CLICK_DISTANCE && deltaY < DOUBLE_CLICK_DISTANCE) {
                            // 双击事件 - 选择焦点
                            handleDoubleClick(e.clientX, e.clientY);
                        }

                        lastClickTime = currentTime;
                        lastClickX = e.clientX;
                        lastClickY = e.clientY;
                    }
                } else if (e.button === 2) { // 右键
                    mouseButtons.right = true;
                }
                lastMouseX = e.clientX;
                lastMouseY = e.clientY;
            });

            canvas.addEventListener('mouseup', (e) => {
                if (e.button === 0) { // 左键
                    mouseButtons.left = false;
                } else if (e.button === 2) { // 右键
                    mouseButtons.right = false;
                }
            });

            canvas.addEventListener('mousemove', (e) => {
                const isAnyMouseDown = mouseButtons.left || mouseButtons.right;
                if (!isAnyMouseDown) return;

                const deltaX = e.clientX - lastMouseX;
                const deltaY = e.clientY - lastMouseY;

                // 如果正在播放动画，暂停动画并同步相机位置
                if (state.animationPlaying && !state.animationPaused) {
                    pauseAnimation();
                }

                // 检查是否左右键同时按下（pan操作）
                const isPanning = mouseButtons.left && mouseButtons.right;

                if (state.cameraMode === 'orbit') {
                    if (isPanning) {
                        // Pan操作：移动焦点
                        const panSpeed = 0.001 * orbitCamera.distance;

                        // 计算相机的right和up向量
                        const cameraRotation = new pc.Quat();
                        cameraRotation.setFromEulerAngles(-orbitCamera.pitch * 180 / Math.PI, orbitCamera.yaw * 180 / Math.PI, 0);

                        const right = new pc.Vec3();
                        const up = new pc.Vec3();
                        cameraRotation.transformVector(pc.Vec3.RIGHT, right);
                        cameraRotation.transformVector(pc.Vec3.UP, up);

                        // 根据鼠标移动调整焦点
                        const panOffset = new pc.Vec3();
                        panOffset.add(right.clone().mulScalar(-deltaX * panSpeed));
                        panOffset.add(up.clone().mulScalar(deltaY * panSpeed));
                        orbitCamera.focus.add(panOffset);
                    } else {
                        // 旋转操作（只有左键或只有右键）
                        orbitCamera.yaw += deltaX * 0.01;
                        orbitCamera.pitch += deltaY * 0.01;
                        orbitCamera.pitch = Math.max(-Math.PI/2, Math.min(Math.PI/2, orbitCamera.pitch));
                    }
                } else if (state.cameraMode === 'fly') {
                    if (isPanning) {
                        // Pan操作：移动位置
                        const panSpeed = 0.01;

                        // 计算相机的right和up向量
                        const cameraRotation = new pc.Quat();
                        cameraRotation.setFromEulerAngles(flyCamera.rotation.x, flyCamera.rotation.y, flyCamera.rotation.z);

                        const right = new pc.Vec3();
                        const up = new pc.Vec3();
                        cameraRotation.transformVector(pc.Vec3.RIGHT, right);
                        cameraRotation.transformVector(pc.Vec3.UP, up);

                        // 根据鼠标移动调整位置
                        const panOffset = new pc.Vec3();
                        panOffset.add(right.clone().mulScalar(-deltaX * panSpeed));
                        panOffset.add(up.clone().mulScalar(deltaY * panSpeed));
                        flyCamera.position.add(panOffset);
                    } else {
                        // 旋转操作（只有左键或只有右键）
                        flyCamera.rotation.y += deltaX * 0.1;
                        flyCamera.rotation.x += deltaY * 0.1;
                        flyCamera.rotation.x = Math.max(-90, Math.min(90, flyCamera.rotation.x));
                    }
                }

                lastMouseX = e.clientX;
                lastMouseY = e.clientY;
            });

            canvas.addEventListener('wheel', (e) => {
                e.preventDefault();

                // 如果正在播放动画，暂停动画并同步相机位置
                if (state.animationPlaying && !state.animationPaused) {
                    pauseAnimation();
                }

                if (state.cameraMode === 'orbit') {
                    orbitCamera.distance += e.deltaY * 0.01;
                    // 设置距离限制：最小0.01，最大10.0（参考编辑器配置）
                    orbitCamera.distance = Math.max(0.01, Math.min(10.0, orbitCamera.distance));
                } else if (state.cameraMode === 'fly') {
                    // 沿着相机前向方向移动
                    const moveSpeed = 0.01;
                    const cameraRotation = new pc.Quat();
                    cameraRotation.setFromEulerAngles(flyCamera.rotation.x, flyCamera.rotation.y, flyCamera.rotation.z);

                    const forward = new pc.Vec3();
                    cameraRotation.transformVector(pc.Vec3.FORWARD, forward);

                    const moveOffset = forward.clone().mulScalar(-e.deltaY * moveSpeed);
                    flyCamera.position.add(moveOffset);
                }
            });

            // 屏蔽右键菜单
            canvas.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                return false;
            });

            // 处理鼠标离开画布的情况，重置鼠标状态
            canvas.addEventListener('mouseleave', () => {
                mouseButtons.left = false;
                mouseButtons.right = false;
            });

            // 处理窗口失去焦点的情况，重置鼠标状态
            window.addEventListener('blur', () => {
                mouseButtons.left = false;
                mouseButtons.right = false;
            });

            // 监听来自父窗口的消息
            window.addEventListener('message', (event) => {
                const { type, data } = event.data;

                switch (type) {
                    case 'initialize':
                        setCameraMode(data.cameraMode);
                        state.animationPaused = data.animationPaused;
                        break;
                    case 'setCameraMode':
                        setCameraMode(data);
                        break;
                    case 'reset':
                        currentController.reset();
                        animCamera?.reset();
                        stopAnimation();
                        break;
                    case 'playAnimation':
                        playAnimation();
                        break;
                    case 'pauseAnimation':
                        pauseAnimation();
                        break;
                    case 'setAnimationTime':
                        state.animationTime = data;
                        if (animCamera) {
                            animCamera.cursor.value = data;

                            // 手动调用update来重新评估spline并更新position/target
                            animCamera.update(0, null);
                        }
                        break;
                }
            });

            // 加载splat数据
            const loadSplatData = async () => {
                try {
                    // 使用PlayCanvas的GSplat加载器加载PLY数据
                    const asset = new pc.Asset('preview-splat', 'gsplat', {
                        url: plyUrl,
                        filename: 'preview-splat.ply'
                    });

                    asset.ready(() => {
                        if (!asset.resource) {
                            console.error("资源加载失败，asset.resource 为 null");
                            return;
                        }
                        // 创建GSplat实体
                        const splatEntity = asset.resource.instantiate();
                        app.root.addChild(splatEntity);

                        // 初始化相机控制器
                        initCameraControllers();

                        // 简化的相机初始化 - 直接使用原始数据
                        const bbox = splatEntity.gsplat.instance.splat.aabb;

                        // 计算 framePose：基于场景包围盒的默认视角（与编辑模式一致）
                        const sceneSize = bbox.halfExtents.length();
                        const fov = settings.camera?.fov || 50;

                        // 计算 fovFactor（与编辑模式camera.ts中的逻辑一致）
                        const width = app.graphicsDevice.width;
                        const height = app.graphicsDevice.height;
                        const aspect = (width && height) ? camera.camera.horizontalFov ? height / width : width / height : 1;
                        const adjustedFov = 2 * Math.atan(Math.tan(fov * Math.PI / 180 * 0.5) * aspect);
                        const fovFactor = Math.sin(adjustedFov * 0.5);

                        // 使用与编辑模式相同的距离计算方式
                        const distance = sceneSize / fovFactor;
                        const framePose = {
                            position: new pc.Vec3(2, 1, 2).normalize().mulScalar(distance).add(bbox.center),
                            target: bbox.center.clone()
                        };

                        // 计算 resetPose：基于设置的位置和目标
                        const resetPose = {
                            position: settings.camera?.position ?
                                new pc.Vec3(settings.camera.position[0], settings.camera.position[1], settings.camera.position[2]) :
                                new pc.Vec3(2, 1, 2),
                            target: settings.camera?.target ?
                                new pc.Vec3(settings.camera.target[0], settings.camera.target[1], settings.camera.target[2]) :
                                new pc.Vec3(0, 0, 0)
                        };

                        // 决定使用哪个pose（与supersplat-viewer逻辑一致）
                        const useReset = settings.camera?.position || settings.camera?.target || bbox.halfExtents.length() > 100;
                        const userStart = useReset ? resetPose : framePose;

                        // 设置相机位置
                        camera.setPosition(userStart.position.x, userStart.position.y, userStart.position.z);
                        camera.lookAt(userStart.target.x, userStart.target.y, userStart.target.z);

                        // 设置orbit相机
                        orbitCamera.focus.copy(userStart.target);
                        const cameraDistance = new pc.Vec3().sub2(userStart.position, userStart.target).length();
                        orbitCamera.distance = cameraDistance;

                        const dir = new pc.Vec3().sub2(userStart.position, userStart.target).normalize();
                        orbitCamera.pitch = Math.asin(Math.max(-1, Math.min(1, dir.y)));
                        orbitCamera.yaw = Math.atan2(dir.x, dir.z);

                        // 设置fly相机
                        flyCamera.position.copy(userStart.position);
                        flyCamera.rotation.x = -Math.asin(dir.y) * 180 / Math.PI;
                        flyCamera.rotation.y = Math.atan2(dir.x, dir.z) * 180 / Math.PI;

                        console.log('相机初始化完成:', {
                            useReset,
                            sceneSize,
                            position: userStart.position,
                            target: userStart.target,
                            distance: cameraDistance
                        });

                        // 创建动画相机（在splatEntity创建后）
                        if (settings.animTracks && settings.animTracks.length > 0) {
                            const track = settings.animTracks[0];
                            if (track && track.keyframes) {
                                try {
                                    // 直接使用原始关键帧数据，不做复杂转换
                                    animCamera = AnimCamera.fromTrack(track);
                                    state.hasAnimation = true;
                                    state.animationDuration = track.duration;
                                } catch (error) {
                                    console.warn('创建动画相机失败:', error);
                                    state.hasAnimation = false;
                                }
                            }
                        } else {
                            console.log('没有动画轨道数据');
                        }

                        // 保存初始状态，以便reset时能回到这个位置
                        orbitCamera.saveInitialState();
                        flyCamera.saveInitialState();

                        // 初始化相机位置并触发第一次排序（与supersplat-viewer一致）
                        camera.setPosition(userStart.position.x, userStart.position.y, userStart.position.z);
                        camera.lookAt(userStart.target.x, userStart.target.y, userStart.target.z);
                        splatEntity.gsplat?.instance?.sort(camera);

                        // 监听 gsplat sorter 更新事件（与supersplat-viewer一致）
                        splatEntity.gsplat?.instance?.sorter?.on('updated', () => {
                            // request frame render when sorting changes
                            app.renderNextFrame = true;

                            if (!state.readyToRender) {
                                // we're ready to render once the first sort has completed
                                state.readyToRender = true;

                                // wait for the first valid frame to complete rendering
                                const frameHandle = app.on('frameend', () => {
                                    frameHandle.off();

                                    // 发送viewer就绪消息
                                    window.parent.postMessage({
                                        type: 'viewerReady'
                                    }, '*');

                                    // 发送动画信息
                                    window.parent.postMessage({
                                        type: 'animationInfo',
                                        data: {
                                            hasAnimation: state.hasAnimation,
                                            duration: state.animationDuration
                                        }
                                    }, '*');

                                    // 隐藏加载界面
                                    document.getElementById('loading').style.display = 'none';
                                });
                            }
                        });
                    });

                    asset.on('error', (err) => {
                        console.error('加载splat资源失败:', err);

                        // 初始化相机控制器
                        initCameraControllers();

                        // 保存初始状态（使用默认值）
                        orbitCamera.saveInitialState();
                        flyCamera.saveInitialState();

                        // 设置为可以渲染
                        state.readyToRender = true;

                        // 发送viewer就绪消息
                        window.parent.postMessage({
                            type: 'viewerReady'
                        }, '*');

                        // 发送动画信息
                        window.parent.postMessage({
                            type: 'animationInfo',
                            data: {
                                hasAnimation: state.hasAnimation,
                                duration: state.animationDuration
                            }
                        }, '*');

                        // 隐藏加载界面
                        document.getElementById('loading').style.display = 'none';
                    });

                    app.assets.add(asset);
                    app.assets.load(asset);

                } catch (error) {
                    console.error('加载splat数据失败:', error);

                    // 初始化相机控制器
                    initCameraControllers();

                    // 保存初始状态（使用默认值）
                    orbitCamera.saveInitialState();
                    flyCamera.saveInitialState();

                    // 设置为可以渲染
                    state.readyToRender = true;

                    // 发送viewer就绪消息
                    window.parent.postMessage({
                        type: 'viewerReady'
                    }, '*');

                    // 发送动画信息
                    window.parent.postMessage({
                        type: 'animationInfo',
                        data: {
                            hasAnimation: state.hasAnimation,
                            duration: state.animationDuration
                        }
                    }, '*');

                    // 隐藏加载界面
                    document.getElementById('loading').style.display = 'none';
                }
            };

            // 更新循环
            const pose = new Pose();
            const animPose = new Pose();

            app.on('update', (dt) => {
                // 更新动画相机（如果有动画且正在播放）
                if (animCamera && state.animationPlaying && !state.animationPaused) {
                    animCamera.update(dt);
                    state.animationTime = animCamera.cursor.value;

                    // 发送时间更新消息
                    window.parent.postMessage({
                        type: 'animationTimeUpdate',
                        data: { time: state.animationTime }
                    }, '*');
                }

                // 更新当前相机控制器
                if (currentController) {
                    currentController.update(dt);
                }

                // 决定最终的相机位置
                if (state.cameraMode === 'anim' && animCamera) {
                    // 如果在动画模式，始终使用动画相机的位置（无论是否暂停）
                    animCamera.getPose(pose);
                } else {
                    // 否则使用当前控制器的位置
                    if (state.cameraMode === 'orbit') {
                        orbitCamera?.getPose(pose);
                    } else if (state.cameraMode === 'fly') {
                        flyCamera?.getPose(pose);
                    }
                }

                // 应用相机位置
                camera.setPosition(pose.position);
                camera.setRotation(pose.rotation);
            });

            // 启动应用
            app.start();

            // 加载数据
            loadSplatData();
        </script>
    </head>
    <body>
        <div id="loading">
            <div id="loading-spinner"></div>
            <div id="loading-text">加载预览中...</div>
        </div>
        <div class="preview-notice">Unicity 预览模式</div>
    </body>
</html>`;

            return viewerHtml;
        } catch (error) {
            console.error('生成增强预览失败:', error);
            throw error;
        } finally {
            events.fire('stopSpinner');
        }
    });
};

export { initFileHandler };
